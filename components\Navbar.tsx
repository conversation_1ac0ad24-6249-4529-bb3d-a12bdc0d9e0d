import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import {
  HomeIcon,
  ChartBarIcon,
  MagnifyingGlassIcon,
  Bars3Icon,
  SunIcon,
  MoonIcon,
  PlusIcon
} from '../constants';
import UserDropdown from './UserDropdown';
import SearchBar from './SearchBar';
import NotificationDropdown from './NotificationDropdown';
import MobileMenu from './MobileMenu';

interface NavbarProps {
  onAddCategory: () => void;
  onAddTransaction?: () => void;
  currentSection?: 'dashboard' | 'categories' | 'reports';
  onSectionChange?: (section: 'dashboard' | 'categories' | 'reports') => void;
}

const Navbar: React.FC<NavbarProps> = ({
  onAddCategory,
  onAddTransaction,
  currentSection = 'dashboard',
  onSectionChange
}) => {
  const { user } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(true); // Default to dark mode
  const [notifications] = useState([
    { id: 1, message: 'Budget limit reached for Groceries', type: 'warning' as const, time: '2 min ago' },
    { id: 2, message: 'Monthly report is ready', type: 'info' as const, time: '1 hour ago' },
  ]);

  const navItems = [
    { id: 'dashboard', label: 'Dashboard', icon: HomeIcon, href: '#dashboard' },
    { id: 'categories', label: 'Categories', icon: ChartBarIcon, href: '#categories' },
    { id: 'reports', label: 'Reports', icon: ChartBarIcon, href: '#reports' },
  ];

  const handleSectionClick = (sectionId: string) => {
    if (onSectionChange && (sectionId === 'dashboard' || sectionId === 'categories' || sectionId === 'reports')) {
      onSectionChange(sectionId);
    }
  };

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
    // In a real app, you'd persist this to localStorage and update the document class
    // document.documentElement.classList.toggle('dark');
  };

  if (!user) return null;

  return (
    <>
      {/* Main Navbar */}
      <nav className="bg-slate-800/95 backdrop-blur-sm border-b border-slate-700/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            
            {/* Left Section - Logo & Navigation */}
            <div className="flex items-center space-x-8">
              {/* Logo */}
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-sky-400 to-sky-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">BP</span>
                </div>
                <div className="hidden sm:block">
                  <h1 className="text-xl font-bold text-white">Budget Planner</h1>
                  <p className="text-xs text-slate-400 -mt-1">Financial Control</p>
                </div>
              </div>

              {/* Desktop Navigation */}
              <div className="hidden md:flex items-center space-x-1">
                {navItems.map((item) => {
                  const Icon = item.icon;
                  const isActive = currentSection === item.id;
                  return (
                    <button
                      key={item.id}
                      onClick={() => handleSectionClick(item.id)}
                      className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                        isActive
                          ? 'bg-sky-600 text-white shadow-lg shadow-sky-600/25'
                          : 'text-slate-300 hover:text-white hover:bg-slate-700/50'
                      }`}
                    >
                      <Icon className="w-4 h-4" />
                      <span>{item.label}</span>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Center Section - Search */}
            <div className="hidden lg:flex flex-1 max-w-lg mx-8">
              <SearchBar 
                onFocus={() => setIsSearchFocused(true)}
                onBlur={() => setIsSearchFocused(false)}
                isFocused={isSearchFocused}
              />
            </div>

            {/* Right Section - Actions & User */}
            <div className="flex items-center space-x-3">

              {/* Quick Add Buttons */}
              <div className="hidden sm:flex items-center space-x-2">
                <button
                  onClick={onAddCategory}
                  className="flex items-center space-x-2 bg-sky-600 hover:bg-sky-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 shadow-lg shadow-sky-600/25"
                >
                  <PlusIcon className="w-4 h-4" />
                  <span className="hidden lg:inline">Category</span>
                </button>

                {onAddTransaction && (
                  <button
                    onClick={onAddTransaction}
                    className="flex items-center space-x-2 bg-emerald-600 hover:bg-emerald-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 shadow-lg shadow-emerald-600/25"
                  >
                    <PlusIcon className="w-4 h-4" />
                    <span className="hidden lg:inline">Transaction</span>
                  </button>
                )}
              </div>

              {/* Theme Toggle */}
              <button
                onClick={toggleTheme}
                className="p-2 text-slate-400 hover:text-white hover:bg-slate-700/50 rounded-lg transition-colors duration-200"
                aria-label="Toggle theme"
              >
                {isDarkMode ? <SunIcon className="w-5 h-5" /> : <MoonIcon className="w-5 h-5" />}
              </button>

              {/* Search Icon (Mobile) */}
              <button className="lg:hidden p-2 text-slate-400 hover:text-white hover:bg-slate-700/50 rounded-lg transition-colors duration-200">
                <MagnifyingGlassIcon className="w-5 h-5" />
              </button>

              {/* Notifications */}
              <NotificationDropdown notifications={notifications} />

              {/* User Dropdown */}
              <UserDropdown user={user} />

              {/* Mobile Menu Button */}
              <button
                onClick={() => setIsMobileMenuOpen(true)}
                className="md:hidden p-2 text-slate-400 hover:text-white hover:bg-slate-700/50 rounded-lg transition-colors duration-200"
                aria-label="Open menu"
              >
                <Bars3Icon className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Search Bar */}
        <div className="lg:hidden px-4 pb-3">
          <SearchBar 
            onFocus={() => setIsSearchFocused(true)}
            onBlur={() => setIsSearchFocused(false)}
            isFocused={isSearchFocused}
          />
        </div>
      </nav>

      {/* Mobile Menu */}
      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
        navItems={navItems}
        currentSection={currentSection}
        onSectionChange={handleSectionClick}
        onAddCategory={onAddCategory}
        onAddTransaction={onAddTransaction}
      />
    </>
  );
};

export default Navbar;
