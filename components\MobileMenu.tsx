import React, { useEffect } from 'react';
import { XMarkIcon, PlusIcon } from '../constants';

interface NavItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  href: string;
}

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
  navItems: NavItem[];
  currentSection: string;
  onSectionChange: (section: string) => void;
  onAddCategory: () => void;
  onAddTransaction?: () => void;
}

const MobileMenu: React.FC<MobileMenuProps> = ({
  isOpen,
  onClose,
  navItems,
  currentSection,
  onSectionChange,
  onAddCategory,
  onAddTransaction
}) => {
  // Prevent body scroll when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Close menu on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  const handleSectionClick = (sectionId: string) => {
    onSectionChange(sectionId);
    onClose();
  };

  const handleAddCategory = () => {
    onAddCategory();
    onClose();
  };

  const handleAddTransaction = () => {
    if (onAddTransaction) {
      onAddTransaction();
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 md:hidden"
        onClick={onClose}
      />

      {/* Mobile Menu */}
      <div className="fixed inset-y-0 right-0 w-80 max-w-[85vw] bg-slate-800 shadow-xl z-50 md:hidden transform transition-transform duration-300 ease-in-out">
        
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-slate-700/50">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-br from-sky-400 to-sky-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">BP</span>
            </div>
            <div>
              <h2 className="text-white font-semibold text-lg">Menu</h2>
              <p className="text-slate-400 text-xs">Budget Planner</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-slate-400 hover:text-white hover:bg-slate-700/50 rounded-lg transition-colors duration-200"
            aria-label="Close menu"
          >
            <XMarkIcon className="w-5 h-5" />
          </button>
        </div>

        {/* Navigation Items */}
        <div className="p-4 space-y-2">
          <h3 className="text-slate-400 text-xs font-semibold uppercase tracking-wider mb-3">
            Navigation
          </h3>
          
          {navItems.map((item) => {
            const Icon = item.icon;
            const isActive = currentSection === item.id;
            
            return (
              <button
                key={item.id}
                onClick={() => handleSectionClick(item.id)}
                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-all duration-200 ${
                  isActive
                    ? 'bg-sky-600 text-white shadow-lg shadow-sky-600/25'
                    : 'text-slate-300 hover:text-white hover:bg-slate-700/50'
                }`}
              >
                <Icon className="w-5 h-5" />
                <span className="font-medium">{item.label}</span>
                {isActive && (
                  <div className="ml-auto w-2 h-2 bg-white rounded-full"></div>
                )}
              </button>
            );
          })}
        </div>

        {/* Quick Actions */}
        <div className="p-4 border-t border-slate-700/50">
          <h3 className="text-slate-400 text-xs font-semibold uppercase tracking-wider mb-3">
            Quick Actions
          </h3>

          <div className="space-y-2">
            <button
              onClick={handleAddCategory}
              className="w-full flex items-center space-x-3 px-4 py-3 bg-sky-600 hover:bg-sky-700 text-white rounded-lg transition-colors duration-200 shadow-lg shadow-sky-600/25"
            >
              <PlusIcon className="w-5 h-5" />
              <span className="font-medium">Add Category</span>
            </button>

            {onAddTransaction && (
              <button
                onClick={handleAddTransaction}
                className="w-full flex items-center space-x-3 px-4 py-3 bg-emerald-600 hover:bg-emerald-700 text-white rounded-lg transition-colors duration-200 shadow-lg shadow-emerald-600/25"
              >
                <PlusIcon className="w-5 h-5" />
                <span className="font-medium">Add Transaction</span>
              </button>
            )}
          </div>
        </div>

        {/* App Info */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-slate-700/50 bg-slate-800/95">
          <div className="text-center">
            <p className="text-slate-400 text-xs">
              Personal Budget Planner
            </p>
            <p className="text-slate-500 text-xs mt-1">
              Take control of your finances
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

export default MobileMenu;
