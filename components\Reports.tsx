import React, { useState, useMemo } from 'react';
import { Category, Transaction, ReportDateRange, CategorySpendingData } from '../types';
import { 
  ChartBarIcon, 
  DocumentArrowDownIcon, 
  CalendarIcon,
  CurrencyDollarIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  ExclamationTriangleIcon
} from '../constants';

interface ReportsProps {
  categories: Category[];
  transactions: Transaction[];
  totalIncome: number;
  formatCurrency: (amount: number) => string;
  selectedCurrency: string;
}

const Reports: React.FC<ReportsProps> = ({
  categories,
  transactions,
  totalIncome,
  formatCurrency,
  selectedCurrency
}) => {
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'quarter' | 'year'>('month');
  const [selectedReportType, setSelectedReportType] = useState<'overview' | 'categories' | 'trends' | 'insights'>('overview');

  // Calculate date range based on selected period
  const dateRange = useMemo((): ReportDateRange => {
    const now = new Date();
    const endDate = now.toISOString().split('T')[0];
    let startDate: string;

    switch (selectedPeriod) {
      case 'week':
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        startDate = weekAgo.toISOString().split('T')[0];
        break;
      case 'month':
        const monthAgo = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
        startDate = monthAgo.toISOString().split('T')[0];
        break;
      case 'quarter':
        const quarterAgo = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate());
        startDate = quarterAgo.toISOString().split('T')[0];
        break;
      case 'year':
        const yearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
        startDate = yearAgo.toISOString().split('T')[0];
        break;
      default:
        startDate = endDate;
    }

    return { startDate, endDate };
  }, [selectedPeriod]);

  // Filter transactions by date range
  const filteredTransactions = useMemo(() => {
    return transactions.filter(transaction => {
      const transactionDate = transaction.date;
      return transactionDate >= dateRange.startDate && transactionDate <= dateRange.endDate;
    });
  }, [transactions, dateRange]);

  // Calculate spending data
  const spendingData = useMemo((): CategorySpendingData[] => {
    return categories.map(category => {
      const categoryTransactions = filteredTransactions.filter(t => t.categoryId === category.id && t.type === 'expense');
      const spent = categoryTransactions.reduce((sum, t) => sum + t.amount, 0);
      const allocated = category.allocatedAmount;
      const remaining = allocated - spent;
      const percentage = allocated > 0 ? (spent / allocated) * 100 : 0;

      return {
        categoryId: category.id,
        categoryName: category.name,
        allocated,
        spent,
        remaining,
        percentage
      };
    });
  }, [categories, filteredTransactions]);

  // Calculate totals
  const totals = useMemo(() => {
    const totalSpent = spendingData.reduce((sum, cat) => sum + cat.spent, 0);
    const totalAllocated = spendingData.reduce((sum, cat) => sum + cat.allocated, 0);
    const totalRemaining = totalAllocated - totalSpent;
    const savingsRate = totalIncome > 0 ? ((totalIncome - totalSpent) / totalIncome) * 100 : 0;

    return {
      totalSpent,
      totalAllocated,
      totalRemaining,
      savingsRate,
      transactionCount: filteredTransactions.length
    };
  }, [spendingData, totalIncome, filteredTransactions]);

  // Export functionality
  const exportToCSV = () => {
    const csvContent = [
      ['Category', 'Allocated', 'Spent', 'Remaining', 'Percentage'].join(','),
      ...spendingData.map(cat => [
        cat.categoryName,
        cat.allocated.toString(),
        cat.spent.toString(),
        cat.remaining.toString(),
        cat.percentage.toFixed(2) + '%'
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `budget-report-${selectedPeriod}-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-slate-800 p-6 rounded-lg border border-slate-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-400 text-sm">Total Spent</p>
              <p className="text-2xl font-bold text-red-400">{formatCurrency(totals.totalSpent)}</p>
            </div>
            <TrendingDownIcon className="w-8 h-8 text-red-400" />
          </div>
        </div>

        <div className="bg-slate-800 p-6 rounded-lg border border-slate-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-400 text-sm">Total Allocated</p>
              <p className="text-2xl font-bold text-sky-400">{formatCurrency(totals.totalAllocated)}</p>
            </div>
            <CurrencyDollarIcon className="w-8 h-8 text-sky-400" />
          </div>
        </div>

        <div className="bg-slate-800 p-6 rounded-lg border border-slate-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-400 text-sm">Remaining</p>
              <p className={`text-2xl font-bold ${totals.totalRemaining >= 0 ? 'text-emerald-400' : 'text-red-400'}`}>
                {formatCurrency(totals.totalRemaining)}
              </p>
            </div>
            <TrendingUpIcon className={`w-8 h-8 ${totals.totalRemaining >= 0 ? 'text-emerald-400' : 'text-red-400'}`} />
          </div>
        </div>

        <div className="bg-slate-800 p-6 rounded-lg border border-slate-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-400 text-sm">Savings Rate</p>
              <p className="text-2xl font-bold text-emerald-400">{totals.savingsRate.toFixed(1)}%</p>
            </div>
            <ChartBarIcon className="w-8 h-8 text-emerald-400" />
          </div>
        </div>
      </div>

      {/* Budget vs Actual Chart */}
      <div className="bg-slate-800 p-6 rounded-lg border border-slate-700">
        <h3 className="text-xl font-semibold text-sky-400 mb-4">Budget vs Actual Spending</h3>
        <div className="space-y-4">
          {spendingData.slice(0, 5).map(category => (
            <div key={category.categoryId} className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-slate-200 font-medium">{category.categoryName}</span>
                <div className="text-right">
                  <div className="text-sm text-slate-400">
                    {formatCurrency(category.spent)} / {formatCurrency(category.allocated)}
                  </div>
                  <div className={`text-sm font-medium ${category.percentage > 100 ? 'text-red-400' : 'text-emerald-400'}`}>
                    {category.percentage.toFixed(1)}%
                  </div>
                </div>
              </div>
              <div className="w-full bg-slate-700 rounded-full h-3">
                <div
                  className={`h-3 rounded-full transition-all duration-300 ${
                    category.percentage > 100 ? 'bg-red-500' : 'bg-sky-500'
                  }`}
                  style={{ width: `${Math.min(category.percentage, 100)}%` }}
                />
                {category.percentage > 100 && (
                  <div className="flex items-center mt-1">
                    <ExclamationTriangleIcon className="w-4 h-4 text-red-400 mr-1" />
                    <span className="text-xs text-red-400">Over budget by {formatCurrency(category.spent - category.allocated)}</span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderCategories = () => (
    <div className="bg-slate-800 p-6 rounded-lg border border-slate-700">
      <h3 className="text-xl font-semibold text-sky-400 mb-4">Category Breakdown</h3>
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-slate-700">
              <th className="text-left py-3 px-4 text-slate-300">Category</th>
              <th className="text-right py-3 px-4 text-slate-300">Allocated</th>
              <th className="text-right py-3 px-4 text-slate-300">Spent</th>
              <th className="text-right py-3 px-4 text-slate-300">Remaining</th>
              <th className="text-right py-3 px-4 text-slate-300">Usage %</th>
            </tr>
          </thead>
          <tbody>
            {spendingData.map(category => (
              <tr key={category.categoryId} className="border-b border-slate-700/50">
                <td className="py-3 px-4 text-slate-200 font-medium">{category.categoryName}</td>
                <td className="py-3 px-4 text-right text-slate-300">{formatCurrency(category.allocated)}</td>
                <td className="py-3 px-4 text-right text-red-400">{formatCurrency(category.spent)}</td>
                <td className={`py-3 px-4 text-right ${category.remaining >= 0 ? 'text-emerald-400' : 'text-red-400'}`}>
                  {formatCurrency(category.remaining)}
                </td>
                <td className={`py-3 px-4 text-right font-medium ${category.percentage > 100 ? 'text-red-400' : 'text-emerald-400'}`}>
                  {category.percentage.toFixed(1)}%
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  const renderTrends = () => (
    <div className="bg-slate-800 p-6 rounded-lg border border-slate-700">
      <h3 className="text-xl font-semibold text-sky-400 mb-4">Spending Trends</h3>
      <div className="text-center py-8">
        <ChartBarIcon className="w-16 h-16 text-slate-600 mx-auto mb-4" />
        <p className="text-slate-400">Trend analysis coming soon!</p>
        <p className="text-sm text-slate-500 mt-2">
          Track your spending patterns over time with interactive charts.
        </p>
      </div>
    </div>
  );

  const renderInsights = () => {
    const insights = [];
    
    // Generate insights based on data
    if (totals.savingsRate > 20) {
      insights.push({ type: 'positive', message: 'Excellent savings rate! You\'re saving over 20% of your income.' });
    } else if (totals.savingsRate < 5) {
      insights.push({ type: 'warning', message: 'Low savings rate. Consider reducing expenses or increasing income.' });
    }

    const overBudgetCategories = spendingData.filter(cat => cat.percentage > 100);
    if (overBudgetCategories.length > 0) {
      insights.push({ 
        type: 'warning', 
        message: `${overBudgetCategories.length} categories are over budget: ${overBudgetCategories.map(c => c.categoryName).join(', ')}.` 
      });
    }

    const topSpendingCategory = spendingData.reduce((max, cat) => cat.spent > max.spent ? cat : max, spendingData[0]);
    if (topSpendingCategory) {
      insights.push({ 
        type: 'info', 
        message: `Your highest spending category is "${topSpendingCategory.categoryName}" at ${formatCurrency(topSpendingCategory.spent)}.` 
      });
    }

    return (
      <div className="bg-slate-800 p-6 rounded-lg border border-slate-700">
        <h3 className="text-xl font-semibold text-sky-400 mb-4">Financial Insights</h3>
        <div className="space-y-4">
          {insights.length > 0 ? insights.map((insight, index) => (
            <div 
              key={index}
              className={`p-4 rounded-lg border ${
                insight.type === 'positive' ? 'bg-emerald-500/10 border-emerald-500 text-emerald-300' :
                insight.type === 'warning' ? 'bg-amber-500/10 border-amber-500 text-amber-300' :
                'bg-sky-500/10 border-sky-500 text-sky-300'
              }`}
            >
              {insight.message}
            </div>
          )) : (
            <div className="text-center py-8">
              <p className="text-slate-400">No insights available yet.</p>
              <p className="text-sm text-slate-500 mt-2">
                Add some transactions to get personalized financial insights.
              </p>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-semibold text-sky-400">Financial Reports</h2>
          <p className="text-slate-400 mt-1">
            Analyze your spending patterns and track your financial progress
          </p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-3">
          {/* Period Selector */}
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value as any)}
            className="bg-slate-700 border border-slate-600 text-slate-200 px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-sky-500"
          >
            <option value="week">Last Week</option>
            <option value="month">Last Month</option>
            <option value="quarter">Last Quarter</option>
            <option value="year">Last Year</option>
          </select>

          {/* Export Button */}
          <button
            onClick={exportToCSV}
            className="flex items-center space-x-2 bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg transition-colors duration-200"
          >
            <DocumentArrowDownIcon className="w-4 h-4" />
            <span>Export CSV</span>
          </button>
        </div>
      </div>

      {/* Report Type Tabs */}
      <div className="flex flex-wrap gap-2">
        {[
          { id: 'overview', label: 'Overview' },
          { id: 'categories', label: 'Categories' },
          { id: 'trends', label: 'Trends' },
          { id: 'insights', label: 'Insights' }
        ].map(tab => (
          <button
            key={tab.id}
            onClick={() => setSelectedReportType(tab.id as any)}
            className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
              selectedReportType === tab.id
                ? 'bg-sky-600 text-white'
                : 'bg-slate-700 text-slate-300 hover:bg-slate-600'
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Report Content */}
      {selectedReportType === 'overview' && renderOverview()}
      {selectedReportType === 'categories' && renderCategories()}
      {selectedReportType === 'trends' && renderTrends()}
      {selectedReportType === 'insights' && renderInsights()}
    </div>
  );
};

export default Reports;
